# Usage 
pipeline_simple.py has the base framework to use buffering at different levels
run_picam_showcase.py use this framework and implements a basic face detection

use ubantu in windows to move files/folders to rpi

scp -r "/mnt/c/Users/<USER>/Downloads/hand_landmark_lite--224x224_quant_hailort_hailo8l_1/hand_landmark_lite--224x224_quant_hailort_hailo8l_1" jk@*************:~/degirum-zoo/

showcase_framework_headless.py is the latest one we should use
jk_face * image extract, embed and recong will work correctly

## CHANGE LOG
### 13th Aug 20-25
other files have issues.
showcase_framework.py is working added the realtime falg so video file runs in realtimeand logging added
python showcase_framework.py  --video-file "/home/<USER>/jk/dev/hailotest/videos/people1_30s.mp4" --conf 0.5 --headless --realtime



# HAILO 8 WITH NVME INSTALL



```
sudo nano /boot/firmware/config.txt
dtoverlay=pciex1-compat-pi5,no-mip
sudo reboot

sudo apt update
sudo apt install dkms build-essential bison flex libelf-dev raspberrypi-kernel-headers

sudo dpkg -i hailort-pcie-driver_4.22.0_all.deb
sudo reboot


sudo apt install ffmpeg

rpicam-vid -t 0
```

## Using Degiurm localhost or direct

load the models from the server
```
self.zoo = dg.connect("localhost") 
```

load the model directly - tested in hailo_fast_fps.py

```
dg.connect(dg.LOCAL, zoo_path)
```

## PICAM2 HQ INSTALL IN VIRTUAL




✅ **Step 2: Create a Virtual Environment with Access to System Packages**  
To maintain package isolation while leveraging system-wide installations, create a virtual environment that includes system site packages:

```bash
python3 -m venv --system-site-packages cam-env
````

Then, activate the virtual environment:

```bash
source cam-env/bin/activate
```

This setup allows your virtual environment to access system-wide packages like `libcamera`.

---

✅ **Step 3: Install Additional Python Dependencies**
Within the activated virtual environment, install the required Python packages:

```bash
pip install picamera2
pip install chromadb
```

This command installs the Picamera2 library, which depends on the libcamera bindings.

---

✅ **Step 4: Verify the Installation**
Test your setup by running a simple script:

```python
from picamera2 import Picamera2

camera = Picamera2()
camera.start()
```

If the script runs without errors, your environment is correctly configured.


## Hailo degium server

download all the files from the degium model download and place it this directory.
run the server
https://hub.degirum.com/degirum/hailo?visibility=public



```
degirum server start --zoo ~/degirum-zoo/
```

then run the program - hailo_basic.py

cp -r /home/<USER>/jk/dev/hailotest/model .

# run it
rpicam-vid -t 0

extract to folder
embed into db
show recong
```
python jk_face_image_extract.py --root_folder faces --zoo_path ~/degirum-zoo --duration 20 --count 20
python jk_face_embed_store.py
python jk_face_recong_test.py
```

## important issues
recong is droping frames
we need to buffer it so it works
```
(cam-env) jk@rpi5hq:~/jk/dev/hailotest $ python jk_face_recong_test.py
[INFO] Using Picamera2 backend
[9:48:00.571717566] [162303]  INFO Camera camera_manager.cpp:326 libcamera v0.5.1+100-e53bdf1f
[9:48:00.578945669] [162316]  INFO RPI pisp.cpp:720 libpisp version v1.2.1 981977ff21f3 29-04-2025 (14:13:50)
[9:48:00.588370527] [162316]  INFO RPI pisp.cpp:1179 Registered camera /base/axi/pcie@1000120000/rp1/i2c@80000/imx477@1a to CFE device /dev/media0 and ISP device /dev/media1 using PiSP variant BCM2712_D0
[INFO] Initialization successful.
[INFO] Camera now open.
[INFO] Camera configuration has been adjusted!
[9:48:00.591829109] [162303]  INFO Camera camera.cpp:1205 configuring streams: (0) 640x480-RGB888/sRGB (1) 2028x1520-BGGR_PISP_COMP1/RAW
[9:48:00.591963440] [162316]  INFO RPI pisp.cpp:1483 Sensor: /base/axi/pcie@1000120000/rp1/i2c@80000/imx477@1a - Selected sensor format: 2028x1520-SBGGR12_1X12 - Selected CFE format: 2028x1520-PC1B
[INFO] Configuration successful!
[INFO] Camera started
[INFO] Local inference with local zoo from '/home/<USER>/degirum-zoo' dir
[INFO] Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
[INFO] Elapsed 5.0s, processed frames: 0, approx FPS: 0.00
[INFO] Elapsed 10.0s, processed frames: 29, approx FPS: 2.90
[INFO] Elapsed 15.0s, processed frames: 119, approx FPS: 7.92
[INFO] Elapsed 20.0s, processed frames: 201, approx FPS: 10.04
[INFO] Elapsed 25.0s, processed frames: 290, approx FPS: 11.58
[INFO] Duration reached, stopping.
[INFO] Worker exiting
[INFO] Camera stopped
[INFO] Shutdown complete
[INFO] Camera closed successfully.
(cam-env) jk@rpi5hq:~/jk/dev/hailotest $ python jk_face_recong_test.py
```



